<script setup lang="ts">
import Button from '@/components/common/shared/Button.vue';
import VInput from '@/components/common/shared/VInput.vue';
import { useI18n } from '@/composables/useI18n.ts';
import { InertiaForm } from '@inertiajs/vue3';
import VTextarea from '@/components/common/shared/VTextarea.vue';
import { JobCategoryType, JobFormType } from '@/types/job.ts';
import ButtonLink from '@/components/common/shared/ButtonLink.vue';
import { route } from 'ziggy-js';
import VSelect from '@/components/common/shared/VSelect.vue';
import VFileInput from '@/components/common/shared/VFileInput.vue';
import DatePicker from '@/components/common/shared/DatePicker.vue';
import VRadio from '@/components/common/shared/VRadio.vue';
import { computed, ref, onMounted } from 'vue';

const props = defineProps<{
  isPreview?: boolean;
  formData: InertiaForm<JobFormType>;
  isEdit?: boolean;
  categories: JobCategoryType[];
}>();

const emit = defineEmits<{
  (e: 'openPreview'): void;
  (e: 'close'): void;
  (e: 'submit', form: InertiaForm<JobFormType>): void;
}>();

const { t } = useI18n();
const form = props.formData;

const recruitmentTypeOptions = (window as any).recruitmentTypeOptions;
const jobTypeOptions = (window as any).jobTypeOptions;
const genderOptions = (window as any).genderOptions;
const certificateLevelOptions = (window as any).standardCertificateLevels;
const prefectureOptions = (window as any).prefectureOptions;
const feeTypeOptions = (window as any).feeTypeOptions;
const categoriesOptions = computed(() => [
  ...props.categories.map((category: any) => ({
    label: category.name,
    value: category.id,
  })),
]);
const booleanOptions = (window as any).booleanOptions;

const thumbnailPreview = ref<string | null>(null);
const imagesPreview = ref<string[]>([]);

const createImagePreviews = () => {
  if (!props.isPreview) return;

  if (form.thumbnail && form.thumbnail instanceof File) {
    const reader = new FileReader();
    reader.onload = e => {
      thumbnailPreview.value = e.target?.result as string;
    };
    reader.readAsDataURL(form.thumbnail);
  }

  if (form.images && Array.isArray(form.images)) {
    imagesPreview.value = [];
    form.images.forEach((file: File, index: number) => {
      if (file instanceof File) {
        const reader = new FileReader();
        reader.onload = e => {
          imagesPreview.value[index] = e.target?.result as string;
        };
        reader.readAsDataURL(file);
      }
    });
  }
};

onMounted(() => {
  if (props.isPreview) {
    createImagePreviews();
  }
});

function handleSubmit() {
  emit('submit', form);
}
</script>

<template>
  <form class="flex flex-col" @submit.prevent="handleSubmit">
    <div :class="isPreview ? 'overflow-y-auto custom-scrollbar max-h-[65vh]' : ''">
      <div class="grid grid-cols-2 gap-x-6 gap-y-5 pr-2">
        <v-select
          v-model="form.recruitment_type"
          :error="form.errors.recruitment_type"
          :options="recruitmentTypeOptions"
          class="w-full col-span-2"
          :label="t('models/job.field.recruitment_type')"
          required
          :disabled="isPreview"
        />
        <div v-if="isPreview" class="w-full col-span-2">
          <label class="block text-sm font-medium text-gray-700 mb-1">
            {{ t('models/job.field.thumbnail') }}
            <span class="text-red-500">*</span>
          </label>
          <div v-if="thumbnailPreview" class="border rounded-lg p-4 bg-gray-50">
            <img
              :src="thumbnailPreview"
              :alt="t('models/job.field.thumbnail')"
              class="max-w-full h-48 object-contain mx-auto rounded"
            />
            <p class="text-sm text-gray-500 text-center mt-2">
              {{ form.thumbnail?.name || 'Thumbnail Image' }}
            </p>
          </div>
          <div v-else class="border rounded-lg p-4 bg-gray-50 text-center text-gray-500">
            {{ t('models/job.field.thumbnail') }} - No image selected
          </div>
        </div>
        <v-file-input
          v-else
          v-model="form.thumbnail"
          :error="form.errors.thumbnail"
          accept="image/*"
          class="w-full col-span-2"
          :label="t('models/job.field.thumbnail')"
          required
        />
        <div v-if="isPreview" class="w-full col-span-2">
          <label class="block text-sm font-medium text-gray-700 mb-1">
            {{ t('models/job.field.images') }}
            <span class="text-red-500">*</span>
          </label>
          <div v-if="imagesPreview.length > 0" class="border rounded-lg p-4 bg-gray-50">
            <div class="grid grid-cols-2 md:grid-cols-3 gap-4">
              <div v-for="(preview, index) in imagesPreview" :key="index" class="border rounded-lg p-2 bg-white">
                <img :src="preview" :alt="`Image ${index + 1}`" class="w-full h-32 object-contain rounded" />
                <p class="text-xs text-gray-500 text-center mt-1 truncate">
                  {{ form.images[index]?.name || `Image ${index + 1}` }}
                </p>
              </div>
            </div>
            <p class="text-sm text-gray-600 text-center mt-3">{{ imagesPreview.length }} image(s) selected</p>
          </div>
          <div v-else class="border rounded-lg p-4 bg-gray-50 text-center text-gray-500">
            {{ t('models/job.field.images') }} - No images selected
          </div>
        </div>
        <v-file-input
          v-else
          v-model="form.images"
          multiple
          :error="form.errors.images"
          accept="image/*"
          class="w-full col-span-2"
          :label="t('models/job.field.images')"
          required
        />
        <v-select
          v-model="form.category_id"
          :error="form.errors.category_id"
          :options="categoriesOptions"
          class="w-full col-span-2"
          :label="t('models/job.field.category')"
          :disabled="isPreview"
          required
        />
        <v-input
          v-model="form.employer_name"
          :error="form.errors.employer_name"
          class="w-full col-span-2"
          :label="t('models/job.field.employer_name')"
          required
          :disabled="isPreview"
        />
        <v-input
          v-model="form.employer_email"
          :error="form.errors.employer_email"
          type="email"
          class="w-full col-span-2"
          :label="t('models/job.field.employer_email')"
          required
          :disabled="isPreview"
        />
        <v-input
          v-model="form.employer_phone_number"
          :error="form.errors.employer_phone_number"
          class="w-full col-span-2"
          :label="t('models/job.field.employer_phone_number')"
          required
          :disabled="isPreview"
        />
        <v-select
          v-model="form.type"
          :error="form.errors.type"
          :options="jobTypeOptions"
          class="w-full col-span-2"
          :label="t('models/job.field.type')"
          required
          :disabled="isPreview"
        />
        <v-select
          v-model="form.prefecture"
          :error="form.errors.prefecture"
          :options="prefectureOptions"
          class="pb-6 w-full"
          :label="t('models/job.field.prefecture')"
          required
          :disabled="isPreview"
        />
        <v-input
          v-model="form.address"
          :error="form.errors.address"
          class="w-full"
          :label="t('models/job.field.address')"
          required
          :disabled="isPreview"
        />
        <v-input
          v-model="form.title"
          :error="form.errors.title"
          class="w-full col-span-2"
          :label="t('models/job.field.title')"
          required
          :disabled="isPreview"
        />
        <v-textarea
          v-model="form.description"
          :error="form.errors.description"
          class="col-span-2"
          :label="t('models/job.field.description')"
          required
          :disabled="isPreview"
        />
        <v-textarea
          v-model="form.benefits"
          :error="form.errors.benefits"
          class="col-span-2"
          :label="t('models/job.field.benefits')"
          :disabled="isPreview"
        />
        <v-select
          v-model="form.certificate_level"
          :error="form.errors.certificate_level"
          :options="certificateLevelOptions"
          class="w-full col-span-2"
          :label="t('models/job.field.certificate_level')"
          :disabled="isPreview"
        />
        <date-picker
          v-model="form.time_start"
          :error="form.errors.time_start"
          type="time"
          class="w-full"
          :label="t('models/job.field.time_start')"
          required
          :disabled="isPreview"
        />
        <date-picker
          v-model="form.time_end"
          :error="form.errors.time_end"
          type="time"
          class="w-full"
          :label="t('models/job.field.time_end')"
          required
          :disabled="isPreview"
        />
        <v-select
          v-model="form.salary_type"
          :error="form.errors.salary_type"
          :options="feeTypeOptions"
          class="w-full"
          :label="t('models/job.field.salary_type')"
          required
          :disabled="isPreview"
        />
        <v-input
          v-model="form.salary"
          :error="form.errors.salary"
          class="w-full"
          type="number"
          min="0"
          :label="t('models/job.field.salary')"
          required
          :disabled="isPreview"
        />
        <v-select
          v-model="form.travel_fee_type"
          :error="form.errors.travel_fee_type"
          :options="feeTypeOptions"
          class="w-full"
          :label="t('models/job.field.travel_fee_type')"
          required
          :disabled="isPreview"
        />
        <v-input
          v-model="form.travel_fee"
          :error="form.errors.travel_fee"
          class="w-full"
          type="number"
          min="0"
          :label="t('models/job.field.travel_fee')"
          required
          :disabled="isPreview"
        />
        <v-input
          v-model="form.age"
          :error="form.errors.age"
          class="w-full col-span-2"
          type="number"
          min="0"
          :label="t('models/job.field.age')"
          :disabled="isPreview"
        />
        <v-select
          v-model="form.gender"
          :error="form.errors.gender"
          :options="genderOptions"
          class="w-full col-span-2"
          :label="t('models/job.field.gender')"
          :disabled="isPreview"
        />
        <v-input
          v-model="form.quantity"
          :error="form.errors.quantity"
          class="w-full col-span-2"
          type="number"
          min="0"
          :label="t('models/job.field.quantity')"
          required
          :disabled="isPreview"
        />
        <date-picker
          v-model="form.expired_at"
          :error="form.errors.expired_at"
          type="date"
          class="w-full col-span-2"
          :label="t('models/job.field.expired_at')"
          required
          :disabled="isPreview"
        />
        <date-picker
          v-model="form.job_start_at"
          :error="form.errors.job_start_at"
          type="datetime"
          class="w-full col-span-2"
          :label="t('models/job.field.job_start_at')"
          required
          :disabled="isPreview"
        />
        <v-radio
          v-model="form.is_instant"
          :error="form.errors.is_instant"
          :options="booleanOptions"
          class="w-full"
          :label="t('models/job.field.is_instant')"
          :disabled="isPreview"
        />
        <v-radio
          v-model="form.is_public"
          :error="form.errors.is_public"
          :options="booleanOptions"
          class="w-full"
          :label="t('models/job.field.is_public')"
          :disabled="isPreview"
        />
      </div>
    </div>
    <div v-if="isPreview" class="flex justify-center mt-4">
      <Button class="mr-2" size="sm" variant="primary" type="submit">
        {{ t('common.btn.save') }}
      </Button>
      <Button size="sm" variant="outline" type="button" @click="emit('close')">
        {{ t('common.btn.cancel') }}
      </Button>
    </div>
    <div v-else class="flex justify-center mt-4">
      <Button class="mr-2" size="sm" variant="outline" @click="emit('openPreview')">
        {{ t('common.btn.preview') }}
      </Button>
      <ButtonLink :href="route('admin.job.index')" size="sm" variant="outline">
        {{ t('common.btn.cancel') }}
      </ButtonLink>
    </div>
  </form>
</template>

<style scoped></style>
